#!/bin/bash

echo "=== Django图纸管理系统后端启动脚本 ==="

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "错误: 未找到Python环境"
    exit 1
fi

# 安装依赖
echo "1. 安装Python依赖包..."
pip install -r requirements.txt

# 数据库迁移
echo "2. 执行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 初始化数据
echo "3. 初始化系统数据..."
python manage.py init_data

# 收集静态文件
echo "4. 收集静态文件..."
python manage.py collectstatic --noinput

echo "5. 启动开发服务器..."
echo "管理员账号: admin"
echo "管理员密码: admin123"
echo "访问地址: http://127.0.0.1:8000"
echo "管理后台: http://127.0.0.1:8000/admin"

python manage.py runserver 0.0.0.0:8000
